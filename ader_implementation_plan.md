# Ader 实施计划详细说明

## 🎯 项目概述

Ader 是一个结合 Aider 开源优势和 Augment Code 先进技术的下一代 AI 编程助手。本文档详细说明了从概念到产品的完整实施计划。

## 📅 总体时间线

```
Phase 1: 基础架构 (月 1-3)  → MVP 可用版本
Phase 2: 核心功能 (月 4-6)  → Beta 测试版本  
Phase 3: 企业功能 (月 7-9)  → 生产就绪版本
Phase 4: 生态建设 (月 10-12) → 社区版本
```

## 🚀 Phase 1: 基础架构开发 (月 1-3)

### 月 1: 核心框架搭建

#### 第 1-2 周: 项目初始化
```bash
# 项目结构创建
ader/
├── backend/
│   ├── ader/
│   │   ├── core/           # 核心模块
│   │   ├── api/            # API 接口
│   │   ├── models/         # 数据模型
│   │   └── utils/          # 工具函数
│   ├── tests/              # 测试代码
│   ├── requirements.txt    # 依赖管理
│   └── pyproject.toml      # 项目配置
├── frontend/
│   ├── web-ui/             # Web 界面
│   └── vscode-ext/         # VS Code 插件
├── docs/                   # 文档
├── scripts/                # 脚本工具
└── docker/                 # 容器配置
```

**关键任务**:
- [ ] 设置开发环境和 CI/CD 流水线
- [ ] 创建基础项目结构
- [ ] 配置代码质量工具 (black, flake8, mypy)
- [ ] 设置测试框架 (pytest, coverage)

#### 第 3-4 周: 基础服务框架
```python
# backend/ader/core/base.py
class AderCore:
    """Ader 核心服务基类"""
    
    def __init__(self, config: AderConfig):
        self.config = config
        self.logger = setup_logger()
        self.metrics = MetricsCollector()
        
    async def initialize(self):
        """初始化核心服务"""
        await self.setup_database()
        await self.setup_cache()
        await self.setup_monitoring()

# backend/ader/api/main.py  
from fastapi import FastAPI
from ader.api.routes import query, index, health

app = FastAPI(title="Ader API", version="0.1.0")
app.include_router(query.router, prefix="/api/v1")
app.include_router(index.router, prefix="/api/v1") 
app.include_router(health.router, prefix="/health")
```

**关键任务**:
- [ ] 实现 FastAPI 基础框架
- [ ] 设置数据库连接 (PostgreSQL + Redis)
- [ ] 实现基础 API 路由
- [ ] 添加健康检查和监控端点

### 月 2: 索引引擎开发

#### 第 5-6 周: Tree-sitter 集成
```python
# backend/ader/core/parser.py
class TreeSitterParser:
    """基于 Tree-sitter 的代码解析器"""
    
    def __init__(self):
        self.parsers = self._load_language_parsers()
        
    def _load_language_parsers(self):
        """加载支持的编程语言解析器"""
        return {
            'python': Language.build_library('build/python.so', ['tree-sitter-python']),
            'javascript': Language.build_library('build/js.so', ['tree-sitter-javascript']),
            'typescript': Language.build_library('build/ts.so', ['tree-sitter-typescript']),
            'java': Language.build_library('build/java.so', ['tree-sitter-java']),
            'go': Language.build_library('build/go.so', ['tree-sitter-go']),
        }
        
    async def parse_file(self, file_path: str) -> ParseResult:
        """解析单个文件"""
        language = self._detect_language(file_path)
        parser = Parser()
        parser.set_language(self.parsers[language])
        
        with open(file_path, 'rb') as f:
            source_code = f.read()
            
        tree = parser.parse(source_code)
        symbols = self._extract_symbols(tree, source_code)
        
        return ParseResult(
            file_path=file_path,
            language=language,
            tree=tree,
            symbols=symbols
        )
```

**关键任务**:
- [ ] 集成 Tree-sitter 解析器
- [ ] 支持主流编程语言 (Python, JS, TS, Java, Go)
- [ ] 实现符号提取功能
- [ ] 添加解析结果缓存

#### 第 7-8 周: 基础索引系统
```python
# backend/ader/core/indexer.py
class BasicIndexer:
    """基础索引器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.parser = TreeSitterParser()
        self.cache = CacheManager()
        
    async def index_repository(self, repo_path: str) -> IndexResult:
        """索引整个代码库"""
        files = await self._scan_files(repo_path)
        
        index_tasks = []
        for file_path in files:
            task = self._index_file(file_path)
            index_tasks.append(task)
            
        results = await asyncio.gather(*index_tasks, return_exceptions=True)
        
        # 处理结果和异常
        successful_indexes = [r for r in results if not isinstance(r, Exception)]
        failed_indexes = [r for r in results if isinstance(r, Exception)]
        
        return IndexResult(
            total_files=len(files),
            successful=len(successful_indexes),
            failed=len(failed_indexes),
            index_data=successful_indexes
        )
        
    async def _index_file(self, file_path: str) -> FileIndex:
        """索引单个文件"""
        # 1. 检查缓存
        cached_result = await self.cache.get_file_index(file_path)
        if cached_result and not self._is_file_modified(file_path, cached_result):
            return cached_result
            
        # 2. 解析文件
        parse_result = await self.parser.parse_file(file_path)
        
        # 3. 创建索引
        file_index = FileIndex(
            file_path=file_path,
            symbols=parse_result.symbols,
            last_modified=os.path.getmtime(file_path),
            checksum=self._calculate_checksum(file_path)
        )
        
        # 4. 存储到数据库
        await self.db.store_file_index(file_index)
        
        # 5. 更新缓存
        await self.cache.set_file_index(file_path, file_index)
        
        return file_index
```

**关键任务**:
- [ ] 实现基础文件索引功能
- [ ] 添加增量索引支持
- [ ] 实现索引缓存机制
- [ ] 添加并发处理能力

### 月 3: 查询引擎开发

#### 第 9-10 周: 基础查询功能
```python
# backend/ader/core/query_engine.py
class QueryEngine:
    """查询引擎"""
    
    def __init__(self, indexer: BasicIndexer):
        self.indexer = indexer
        self.searcher = SymbolSearcher()
        
    async def search(self, query: str, repo_path: str) -> SearchResult:
        """执行搜索查询"""
        # 1. 查询预处理
        processed_query = self._preprocess_query(query)
        
        # 2. 符号搜索
        symbol_matches = await self.searcher.search_symbols(
            processed_query, repo_path
        )
        
        # 3. 文件搜索
        file_matches = await self.searcher.search_files(
            processed_query, repo_path
        )
        
        # 4. 合并和排序结果
        all_matches = symbol_matches + file_matches
        ranked_matches = self._rank_results(all_matches, processed_query)
        
        return SearchResult(
            query=query,
            matches=ranked_matches,
            total_count=len(ranked_matches)
        )
        
    def _rank_results(self, matches: List[Match], query: ProcessedQuery) -> List[Match]:
        """结果排序"""
        for match in matches:
            # 计算相关性分数
            match.relevance_score = self._calculate_relevance(match, query)
            
        # 按相关性排序
        return sorted(matches, key=lambda m: m.relevance_score, reverse=True)
```

**关键任务**:
- [ ] 实现基础符号搜索
- [ ] 添加文件内容搜索
- [ ] 实现结果排序算法
- [ ] 添加搜索结果缓存

#### 第 11-12 周: API 接口和测试
```python
# backend/ader/api/routes/query.py
from fastapi import APIRouter, Depends
from ader.core.query_engine import QueryEngine

router = APIRouter(prefix="/query", tags=["query"])

@router.post("/search")
async def search_code(
    request: SearchRequest,
    query_engine: QueryEngine = Depends(get_query_engine)
) -> SearchResponse:
    """代码搜索接口"""
    try:
        result = await query_engine.search(
            query=request.query,
            repo_path=request.repo_path
        )
        
        return SearchResponse(
            success=True,
            data=result,
            message="Search completed successfully"
        )
        
    except Exception as e:
        logger.error(f"Search failed: {e}")
        return SearchResponse(
            success=False,
            error=str(e),
            message="Search failed"
        )

@router.post("/index")
async def index_repository(
    request: IndexRequest,
    indexer: BasicIndexer = Depends(get_indexer)
) -> IndexResponse:
    """代码库索引接口"""
    result = await indexer.index_repository(request.repo_path)
    
    return IndexResponse(
        success=True,
        data=result,
        message=f"Indexed {result.successful} files successfully"
    )
```

**关键任务**:
- [ ] 完善 API 接口设计
- [ ] 添加请求验证和错误处理
- [ ] 编写单元测试和集成测试
- [ ] 性能测试和优化

## 🔧 Phase 2: 核心功能开发 (月 4-6)

### 月 4: 嵌入模型集成

#### 第 13-14 周: 嵌入模型选择和集成
```python
# backend/ader/core/embedding.py
class EmbeddingEngine:
    """嵌入向量引擎"""
    
    def __init__(self, model_name: str = "microsoft/codebert-base"):
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModel.from_pretrained(model_name)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model.to(self.device)
        
    async def generate_embeddings(self, code_snippets: List[str]) -> List[np.ndarray]:
        """生成代码片段的嵌入向量"""
        embeddings = []
        
        for snippet in code_snippets:
            # 1. 分词
            inputs = self.tokenizer(
                snippet,
                return_tensors="pt",
                max_length=512,
                truncation=True,
                padding=True
            ).to(self.device)
            
            # 2. 生成嵌入
            with torch.no_grad():
                outputs = self.model(**inputs)
                # 使用 [CLS] token 的嵌入作为代码片段的表示
                embedding = outputs.last_hidden_state[:, 0, :].cpu().numpy()
                embeddings.append(embedding.squeeze())
                
        return embeddings
        
    async def similarity_search(self, query_embedding: np.ndarray, k: int = 10) -> List[SimilarityResult]:
        """相似度搜索"""
        # 这里需要集成向量数据库 (如 Faiss, Pinecone, 或 Weaviate)
        # 暂时使用简单的内存搜索作为示例
        pass
```

**关键任务**:
- [ ] 集成 CodeBERT/GraphCodeBERT 模型
- [ ] 实现代码嵌入生成
- [ ] 集成向量数据库 (Faiss/Weaviate)
- [ ] 实现相似度搜索

#### 第 15-16 周: 语义搜索功能
```python
# backend/ader/core/semantic_search.py
class SemanticSearchEngine:
    """语义搜索引擎"""
    
    def __init__(self, embedding_engine: EmbeddingEngine):
        self.embedding_engine = embedding_engine
        self.vector_store = VectorStore()
        
    async def build_semantic_index(self, file_indexes: List[FileIndex]):
        """构建语义索引"""
        for file_index in file_indexes:
            # 1. 提取代码片段
            code_snippets = self._extract_code_snippets(file_index)
            
            # 2. 生成嵌入向量
            embeddings = await self.embedding_engine.generate_embeddings(code_snippets)
            
            # 3. 存储到向量数据库
            await self.vector_store.store_embeddings(
                file_path=file_index.file_path,
                snippets=code_snippets,
                embeddings=embeddings
            )
            
    async def semantic_search(self, query: str, k: int = 10) -> List[SemanticMatch]:
        """语义搜索"""
        # 1. 生成查询嵌入
        query_embedding = await self.embedding_engine.generate_embeddings([query])
        
        # 2. 向量相似度搜索
        similar_results = await self.vector_store.similarity_search(
            query_embedding[0], k=k
        )
        
        # 3. 构建搜索结果
        semantic_matches = []
        for result in similar_results:
            match = SemanticMatch(
                file_path=result.file_path,
                code_snippet=result.code_snippet,
                similarity_score=result.similarity_score,
                line_number=result.line_number
            )
            semantic_matches.append(match)
            
        return semantic_matches
```

**关键任务**:
- [ ] 实现语义索引构建
- [ ] 集成语义搜索功能
- [ ] 优化搜索性能
- [ ] 添加搜索结果重排序

### 月 5: 分支感知功能

#### 第 17-18 周: Git 集成
```python
# backend/ader/core/git_manager.py
class GitManager:
    """Git 仓库管理器"""
    
    def __init__(self, repo_path: str):
        self.repo_path = repo_path
        self.repo = git.Repo(repo_path)
        
    async def get_current_branch(self) -> str:
        """获取当前分支"""
        return self.repo.active_branch.name
        
    async def get_all_branches(self) -> List[str]:
        """获取所有分支"""
        return [branch.name for branch in self.repo.branches]
        
    async def get_branch_files(self, branch_name: str) -> List[str]:
        """获取指定分支的文件列表"""
        # 临时切换到指定分支
        original_branch = self.repo.active_branch.name
        self.repo.git.checkout(branch_name)
        
        try:
            files = []
            for root, dirs, filenames in os.walk(self.repo_path):
                for filename in filenames:
                    if self._is_code_file(filename):
                        files.append(os.path.join(root, filename))
            return files
        finally:
            # 切换回原分支
            self.repo.git.checkout(original_branch)
            
    async def watch_branch_changes(self, callback):
        """监控分支变化"""
        current_branch = await self.get_current_branch()
        
        while True:
            await asyncio.sleep(1)  # 每秒检查一次
            new_branch = await self.get_current_branch()
            
            if new_branch != current_branch:
                await callback(current_branch, new_branch)
                current_branch = new_branch
```

**关键任务**:
- [ ] 集成 GitPython 库
- [ ] 实现分支检测和切换
- [ ] 添加分支变化监控
- [ ] 实现分支文件差异分析

#### 第 19-20 周: 分支感知索引
```python
# backend/ader/core/branch_aware_indexer.py
class BranchAwareIndexer:
    """分支感知索引器"""
    
    def __init__(self, base_indexer: BasicIndexer, git_manager: GitManager):
        self.base_indexer = base_indexer
        self.git_manager = git_manager
        self.branch_indexes = {}  # branch_name -> BranchIndex
        
    async def initialize(self):
        """初始化分支感知索引"""
        branches = await self.git_manager.get_all_branches()
        
        for branch_name in branches:
            await self._create_branch_index(branch_name)
            
        # 监控分支切换
        await self.git_manager.watch_branch_changes(self._on_branch_switch)
        
    async def _create_branch_index(self, branch_name: str):
        """为指定分支创建索引"""
        # 1. 获取分支文件
        branch_files = await self.git_manager.get_branch_files(branch_name)
        
        # 2. 创建分支特定的索引
        branch_index = BranchIndex(branch_name)
        
        for file_path in branch_files:
            file_index = await self.base_indexer._index_file(file_path)
            branch_index.add_file_index(file_index)
            
        self.branch_indexes[branch_name] = branch_index
        
    async def _on_branch_switch(self, old_branch: str, new_branch: str):
        """处理分支切换"""
        # 1. 保存旧分支状态
        if old_branch in self.branch_indexes:
            await self.branch_indexes[old_branch].save_state()
            
        # 2. 加载新分支索引
        if new_branch not in self.branch_indexes:
            await self._create_branch_index(new_branch)
            
        # 3. 更新活跃分支
        self.active_branch = new_branch
        
    async def search_in_current_branch(self, query: str) -> SearchResult:
        """在当前分支中搜索"""
        current_branch = await self.git_manager.get_current_branch()
        branch_index = self.branch_indexes.get(current_branch)
        
        if not branch_index:
            # 如果分支索引不存在，创建它
            await self._create_branch_index(current_branch)
            branch_index = self.branch_indexes[current_branch]
            
        return await branch_index.search(query)
```

**关键任务**:
- [ ] 实现分支感知索引系统
- [ ] 添加分支切换处理
- [ ] 实现分支特定的搜索
- [ ] 优化分支索引存储

### 月 6: 实时更新功能

#### 第 21-22 周: 文件监控系统
```python
# backend/ader/core/file_watcher.py
class FileWatcher:
    """文件变化监控器"""
    
    def __init__(self, repo_path: str):
        self.repo_path = repo_path
        self.observer = Observer()
        self.event_queue = asyncio.Queue()
        
    async def start_watching(self):
        """开始监控文件变化"""
        event_handler = FileChangeHandler(self.event_queue)
        self.observer.schedule(event_handler, self.repo_path, recursive=True)
        self.observer.start()
        
        # 启动事件处理协程
        asyncio.create_task(self._process_events())
        
    async def _process_events(self):
        """处理文件变化事件"""
        while True:
            try:
                event = await asyncio.wait_for(self.event_queue.get(), timeout=1.0)
                await self._handle_file_change(event)
            except asyncio.TimeoutError:
                continue
                
    async def _handle_file_change(self, event: FileChangeEvent):
        """处理单个文件变化事件"""
        if not self._is_code_file(event.file_path):
            return
            
        if event.event_type == 'modified':
            await self._on_file_modified(event.file_path)
        elif event.event_type == 'created':
            await self._on_file_created(event.file_path)
        elif event.event_type == 'deleted':
            await self._on_file_deleted(event.file_path)
            
    async def _on_file_modified(self, file_path: str):
        """处理文件修改事件"""
        # 触发增量索引更新
        await self.indexer.incremental_update(file_path)
        
    async def _on_file_created(self, file_path: str):
        """处理文件创建事件"""
        # 添加新文件到索引
        await self.indexer.add_file_to_index(file_path)
        
    async def _on_file_deleted(self, file_path: str):
        """处理文件删除事件"""
        # 从索引中移除文件
        await self.indexer.remove_file_from_index(file_path)
```

**关键任务**:
- [ ] 集成 watchdog 文件监控库
- [ ] 实现文件变化事件处理
- [ ] 添加事件过滤和去重
- [ ] 实现批量事件处理

#### 第 23-24 周: 增量索引更新
```python
# backend/ader/core/incremental_indexer.py
class IncrementalIndexer:
    """增量索引更新器"""
    
    def __init__(self, base_indexer: BasicIndexer):
        self.base_indexer = base_indexer
        self.update_queue = asyncio.Queue()
        self.batch_processor = BatchProcessor()
        
    async def incremental_update(self, file_path: str):
        """增量更新文件索引"""
        # 1. 获取旧索引
        old_index = await self.base_indexer.get_file_index(file_path)
        
        # 2. 重新解析文件
        new_parse_result = await self.base_indexer.parser.parse_file(file_path)
        
        # 3. 计算差异
        diff = self._calculate_index_diff(old_index, new_parse_result)
        
        # 4. 更新索引
        if diff.has_changes():
            await self._apply_index_changes(file_path, diff)
            
        # 5. 更新相关文件的索引
        affected_files = await self._find_affected_files(file_path, diff)
        for affected_file in affected_files:
            await self.update_queue.put(affected_file)
            
    def _calculate_index_diff(self, old_index: FileIndex, new_parse_result: ParseResult) -> IndexDiff:
        """计算索引差异"""
        old_symbols = set(old_index.symbols) if old_index else set()
        new_symbols = set(new_parse_result.symbols)
        
        return IndexDiff(
            added_symbols=new_symbols - old_symbols,
            removed_symbols=old_symbols - new_symbols,
            modified_symbols=self._find_modified_symbols(old_symbols, new_symbols)
        )
        
    async def _apply_index_changes(self, file_path: str, diff: IndexDiff):
        """应用索引变更"""
        # 1. 更新数据库
        await self.base_indexer.db.update_file_index(file_path, diff)
        
        # 2. 更新缓存
        await self.base_indexer.cache.invalidate_file_cache(file_path)
        
        # 3. 更新嵌入向量 (如果有语义搜索)
        if hasattr(self, 'semantic_indexer'):
            await self.semantic_indexer.update_embeddings(file_path, diff)
```

**关键任务**:
- [ ] 实现增量索引更新算法
- [ ] 添加索引差异计算
- [ ] 实现依赖文件更新
- [ ] 优化更新性能

## 📊 Phase 1 成功标准

### 功能完整性
- [ ] 支持 5+ 主流编程语言的代码解析
- [ ] 实现基础的符号搜索和文件搜索
- [ ] 提供 REST API 接口
- [ ] 支持基础的索引缓存

### 性能指标
- [ ] 小型项目 (<1K 文件) 索引时间 <30 秒
- [ ] 查询响应时间 <500ms
- [ ] 内存使用 <2GB (中型项目)
- [ ] 支持并发查询

### 质量保证
- [ ] 单元测试覆盖率 >80%
- [ ] 集成测试通过率 100%
- [ ] 代码质量评分 >8.0
- [ ] 文档完整性 >90%

这个详细的实施计划为 Ader 项目的第一阶段提供了清晰的开发路径，确保在 3 个月内交付一个功能完整的 MVP 版本。
