# Ader 架构设计详细说明

## 🏗️ 系统架构概览

Ader 采用模块化、可扩展的架构设计，结合了 Aider 的本地优先理念和 Augment Code 的先进技术。

```
┌─────────────────────────────────────────────────────────────┐
│                    Ader 系统架构                              │
├─────────────────────────────────────────────────────────────┤
│  用户接口层 (User Interface Layer)                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ VS Code Ext │ │   Web UI    │ │ CLI Tool    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  API 网关层 (API Gateway Layer)                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ FastAPI Gateway + Authentication + Rate Limiting       │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  核心服务层 (Core Services Layer)                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ Query Engine│ │Index Manager│ │Context Gen  │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │Branch Aware │ │Security Mgr │ │Performance  │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  数据处理层 (Data Processing Layer)                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │Tree-sitter  │ │Embedding Eng│ │Graph Builder│           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  存储层 (Storage Layer)                                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ PostgreSQL  │ │    Redis    │ │   Neo4j     │           │
│  │(Metadata)   │ │  (Cache)    │ │ (Relations) │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 核心组件详细设计

### 1. 查询引擎 (QueryEngine)

```python
class QueryEngine:
    """
    查询引擎 - Ader 的核心查询处理组件
    负责理解用户意图并协调各个子系统
    """
    
    def __init__(self):
        self.intent_analyzer = IntentAnalyzer()
        self.context_generator = ContextGenerator()
        self.result_ranker = ResultRanker()
        self.response_formatter = ResponseFormatter()
        
    async def process_query(self, query: Query) -> QueryResult:
        """处理用户查询的主要流程"""
        
        # 1. 查询预处理
        processed_query = await self.preprocess_query(query)
        
        # 2. 意图分析
        intent = await self.intent_analyzer.analyze(processed_query)
        
        # 3. 多阶段检索
        retrieval_results = await self.multi_stage_retrieval(
            processed_query, intent
        )
        
        # 4. 结果排名和过滤
        ranked_results = await self.result_ranker.rank(
            retrieval_results, intent
        )
        
        # 5. 上下文生成
        context = await self.context_generator.generate(
            ranked_results, intent
        )
        
        # 6. 响应格式化
        formatted_response = await self.response_formatter.format(
            context, intent
        )
        
        return QueryResult(
            query=query,
            intent=intent,
            context=context,
            response=formatted_response,
            metadata=self.generate_metadata(query, intent, context)
        )
        
    async def multi_stage_retrieval(self, query, intent):
        """多阶段检索策略"""
        results = []
        
        # 阶段1: 精确匹配
        exact_matches = await self.exact_match_retrieval(query)
        results.extend(exact_matches)
        
        # 阶段2: 语义检索
        semantic_matches = await self.semantic_retrieval(query, intent)
        results.extend(semantic_matches)
        
        # 阶段3: 结构化检索
        structural_matches = await self.structural_retrieval(query, intent)
        results.extend(structural_matches)
        
        # 阶段4: 关系检索
        relation_matches = await self.relation_retrieval(query, intent)
        results.extend(relation_matches)
        
        return self.deduplicate_results(results)
```

### 2. 索引管理器 (IndexManager)

```python
class IndexManager:
    """
    索引管理器 - 管理所有类型的索引
    包括语法索引、语义索引、关系索引等
    """
    
    def __init__(self):
        self.syntax_indexer = SyntaxIndexer()
        self.semantic_indexer = SemanticIndexer()
        self.relation_indexer = RelationIndexer()
        self.branch_manager = BranchAwareManager()
        self.cache_manager = CacheManager()
        
    async def build_comprehensive_index(self, repo_path: str):
        """构建综合索引"""
        
        # 1. 扫描代码库
        files = await self.scan_repository(repo_path)
        
        # 2. 并行构建多种索引
        tasks = [
            self.syntax_indexer.build_index(files),
            self.semantic_indexer.build_index(files),
            self.relation_indexer.build_index(files)
        ]
        
        syntax_index, semantic_index, relation_index = await asyncio.gather(*tasks)
        
        # 3. 构建分支感知索引
        branch_indexes = await self.branch_manager.build_branch_indexes(
            repo_path, [syntax_index, semantic_index, relation_index]
        )
        
        # 4. 缓存索引
        await self.cache_manager.cache_indexes({
            'syntax': syntax_index,
            'semantic': semantic_index,
            'relation': relation_index,
            'branches': branch_indexes
        })
        
        return ComprehensiveIndex(
            syntax=syntax_index,
            semantic=semantic_index,
            relation=relation_index,
            branches=branch_indexes
        )
        
    async def incremental_update(self, file_changes: List[FileChange]):
        """增量更新索引"""
        
        # 1. 分析变更影响
        impact_analysis = await self.analyze_change_impact(file_changes)
        
        # 2. 更新受影响的索引
        update_tasks = []
        
        if impact_analysis.affects_syntax:
            update_tasks.append(
                self.syntax_indexer.update(impact_analysis.syntax_changes)
            )
            
        if impact_analysis.affects_semantics:
            update_tasks.append(
                self.semantic_indexer.update(impact_analysis.semantic_changes)
            )
            
        if impact_analysis.affects_relations:
            update_tasks.append(
                self.relation_indexer.update(impact_analysis.relation_changes)
            )
            
        # 3. 并行执行更新
        await asyncio.gather(*update_tasks)
        
        # 4. 更新分支索引
        await self.branch_manager.update_branch_indexes(impact_analysis)
        
        # 5. 刷新缓存
        await self.cache_manager.invalidate_affected_cache(impact_analysis)
```

### 3. 语义索引器 (SemanticIndexer)

```python
class SemanticIndexer:
    """
    语义索引器 - 构建和维护代码的语义索引
    使用自定义训练的代码嵌入模型
    """
    
    def __init__(self):
        self.embedding_model = CustomCodeEmbeddingModel()
        self.vector_store = VectorStore()
        self.context_extractor = ContextExtractor()
        
    async def build_index(self, files: List[str]):
        """构建语义索引"""
        
        semantic_index = SemanticIndex()
        
        for file_path in files:
            # 1. 提取代码符号
            symbols = await self.extract_symbols(file_path)
            
            # 2. 提取上下文
            contexts = await self.context_extractor.extract_contexts(
                file_path, symbols
            )
            
            # 3. 生成嵌入向量
            embeddings = await self.embedding_model.generate_embeddings(
                symbols, contexts
            )
            
            # 4. 存储到向量数据库
            await self.vector_store.store_embeddings(
                file_path, symbols, embeddings
            )
            
            # 5. 更新索引
            semantic_index.add_file(file_path, symbols, embeddings)
            
        return semantic_index
        
    async def semantic_search(self, query: str, k: int = 10):
        """语义搜索"""
        
        # 1. 生成查询嵌入
        query_embedding = await self.embedding_model.encode_query(query)
        
        # 2. 向量相似度搜索
        similar_vectors = await self.vector_store.similarity_search(
            query_embedding, k=k*2  # 获取更多候选
        )
        
        # 3. 重排序
        reranked_results = await self.rerank_results(
            query, similar_vectors
        )
        
        return reranked_results[:k]
        
    async def rerank_results(self, query: str, candidates):
        """重排序搜索结果"""
        
        reranked = []
        
        for candidate in candidates:
            # 1. 计算多维度相似度
            scores = {
                'semantic': candidate.similarity_score,
                'syntactic': await self.calculate_syntactic_similarity(
                    query, candidate
                ),
                'contextual': await self.calculate_contextual_similarity(
                    query, candidate
                ),
                'popularity': await self.calculate_popularity_score(candidate),
                'recency': await self.calculate_recency_score(candidate)
            }
            
            # 2. 综合评分
            final_score = self.combine_scores(scores)
            
            reranked.append((candidate, final_score))
            
        # 3. 按综合评分排序
        reranked.sort(key=lambda x: x[1], reverse=True)
        
        return [candidate for candidate, score in reranked]
```

### 4. 关系索引器 (RelationIndexer)

```python
class RelationIndexer:
    """
    关系索引器 - 构建和维护代码符号之间的关系图
    实现类似 Aider 的 PageRank 算法但更加智能
    """
    
    def __init__(self):
        self.graph_db = Neo4jGraphDB()
        self.relation_extractor = RelationExtractor()
        self.pagerank_calculator = PageRankCalculator()
        
    async def build_index(self, files: List[str]):
        """构建关系索引"""
        
        relation_graph = RelationGraph()
        
        # 1. 提取所有符号
        all_symbols = {}
        for file_path in files:
            symbols = await self.extract_symbols(file_path)
            all_symbols[file_path] = symbols
            
        # 2. 提取关系
        relations = await self.relation_extractor.extract_relations(all_symbols)
        
        # 3. 构建图
        for relation in relations:
            await relation_graph.add_relation(
                source=relation.source,
                target=relation.target,
                relation_type=relation.type,
                weight=relation.weight
            )
            
        # 4. 计算 PageRank
        pagerank_scores = await self.pagerank_calculator.calculate(
            relation_graph
        )
        
        # 5. 存储到图数据库
        await self.graph_db.store_graph(relation_graph, pagerank_scores)
        
        return RelationIndex(relation_graph, pagerank_scores)
        
    async def find_related_symbols(self, symbol: Symbol, max_depth: int = 3):
        """查找相关符号"""
        
        # 1. 直接关系
        direct_relations = await self.graph_db.get_direct_relations(symbol)
        
        # 2. 间接关系 (通过图遍历)
        indirect_relations = await self.graph_db.traverse_relations(
            symbol, max_depth=max_depth
        )
        
        # 3. 基于 PageRank 的重要性排序
        all_relations = direct_relations + indirect_relations
        sorted_relations = sorted(
            all_relations,
            key=lambda x: x.pagerank_score,
            reverse=True
        )
        
        return sorted_relations
        
    async def calculate_symbol_importance(self, symbol: Symbol):
        """计算符号重要性"""
        
        # 1. PageRank 分数
        pagerank_score = await self.graph_db.get_pagerank_score(symbol)
        
        # 2. 引用频率
        reference_count = await self.graph_db.count_references(symbol)
        
        # 3. 被依赖程度
        dependency_count = await self.graph_db.count_dependencies(symbol)
        
        # 4. 代码质量指标
        quality_score = await self.calculate_quality_metrics(symbol)
        
        # 5. 综合重要性分数
        importance_score = self.combine_importance_factors({
            'pagerank': pagerank_score,
            'references': reference_count,
            'dependencies': dependency_count,
            'quality': quality_score
        })
        
        return importance_score
```

## 🔄 数据流设计

### 查询处理数据流

```
用户查询 → 查询预处理 → 意图分析 → 多阶段检索 → 结果排名 → 上下文生成 → 响应格式化
    ↓           ↓           ↓           ↓           ↓           ↓           ↓
查询标准化   意图分类    精确匹配    相关性评分   上下文选择   响应优化    最终输出
    ↓           ↓           ↓           ↓           ↓           ↓           ↓
实体识别    参数提取    语义检索    多维度排序   Token优化   格式适配    用户界面
    ↓           ↓           ↓           ↓           ↓           ↓
关键词提取  上下文分析  关系检索    去重过滤    解释生成    缓存更新
```

### 索引更新数据流

```
文件变更事件 → 变更检测 → 影响分析 → 增量解析 → 索引更新 → 缓存刷新 → 通知订阅者
      ↓           ↓         ↓         ↓         ↓         ↓         ↓
  文件监控     差异计算   依赖分析   符号提取   向量更新   缓存失效   实时通知
      ↓           ↓         ↓         ↓         ↓         ↓
  事件过滤     变更类型   关系更新   嵌入生成   图更新    性能监控
      ↓           ↓         ↓         ↓         ↓
  批量处理     优先级队列  并行处理   存储更新   状态同步
```

## 🚀 部署架构

### 单机部署 (开发/小团队)

```
┌─────────────────────────────────────┐
│           Ader 单机版                │
├─────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐    │
│  │   Web UI    │ │ VS Code Ext │    │
│  └─────────────┘ └─────────────┘    │
├─────────────────────────────────────┤
│  ┌─────────────────────────────────┐ │
│  │      Ader Core Service          │ │
│  │  (FastAPI + Background Tasks)   │ │
│  └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│  ┌─────────────────────────────────┐ │
│  │     Embedded Databases          │ │
│  │  SQLite + Redis + Embedded Neo4j│ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### 分布式部署 (企业级)

```
┌─────────────────────────────────────────────────────────────┐
│                    Ader 企业版集群                           │
├─────────────────────────────────────────────────────────────┤
│  负载均衡层 (Load Balancer)                                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Nginx/HAProxy + SSL Termination                         │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  API 网关层 (API Gateway)                                   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Kong/Istio + Auth + Rate Limiting + Monitoring         │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  服务层 (Microservices)                                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │Query Service│ │Index Service│ │Context Svc  │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │Auth Service │ │File Service │ │Notify Svc   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  数据层 (Data Layer)                                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │PostgreSQL   │ │Redis Cluster│ │Neo4j Cluster│           │
│  │Cluster      │ │             │ │             │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  基础设施层 (Infrastructure)                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Kubernetes + Docker + Monitoring + Logging             │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

这个架构设计为 Ader 提供了清晰的技术实现路径，确保系统的可扩展性、可维护性和高性能。
