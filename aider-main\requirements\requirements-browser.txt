# This file was autogenerated by uv via the following command:
#    uv pip compile --no-strip-extras --constraint=requirements/common-constraints.txt --output-file=requirements/requirements-browser.txt requirements/requirements-browser.in
altair==5.5.0
    # via
    #   -c requirements/common-constraints.txt
    #   streamlit
attrs==25.3.0
    # via
    #   -c requirements/common-constraints.txt
    #   jsonschema
    #   referencing
blinker==1.9.0
    # via
    #   -c requirements/common-constraints.txt
    #   streamlit
cachetools==5.5.2
    # via
    #   -c requirements/common-constraints.txt
    #   streamlit
certifi==2025.4.26
    # via
    #   -c requirements/common-constraints.txt
    #   requests
charset-normalizer==3.4.2
    # via
    #   -c requirements/common-constraints.txt
    #   requests
click==8.2.1
    # via
    #   -c requirements/common-constraints.txt
    #   streamlit
gitdb==4.0.12
    # via
    #   -c requirements/common-constraints.txt
    #   gitpython
gitpython==3.1.44
    # via
    #   -c requirements/common-constraints.txt
    #   streamlit
idna==3.10
    # via
    #   -c requirements/common-constraints.txt
    #   requests
jinja2==3.1.6
    # via
    #   -c requirements/common-constraints.txt
    #   altair
    #   pydeck
jsonschema==4.24.0
    # via
    #   -c requirements/common-constraints.txt
    #   altair
jsonschema-specifications==2025.4.1
    # via
    #   -c requirements/common-constraints.txt
    #   jsonschema
markupsafe==3.0.2
    # via
    #   -c requirements/common-constraints.txt
    #   jinja2
narwhals==1.41.1
    # via
    #   -c requirements/common-constraints.txt
    #   altair
numpy==1.26.4
    # via
    #   -c requirements/common-constraints.txt
    #   pandas
    #   pydeck
    #   streamlit
packaging==24.2
    # via
    #   -c requirements/common-constraints.txt
    #   altair
    #   streamlit
pandas==2.3.0
    # via
    #   -c requirements/common-constraints.txt
    #   streamlit
pillow==11.2.1
    # via
    #   -c requirements/common-constraints.txt
    #   streamlit
protobuf==5.29.5
    # via
    #   -c requirements/common-constraints.txt
    #   streamlit
pyarrow==20.0.0
    # via
    #   -c requirements/common-constraints.txt
    #   streamlit
pydeck==0.9.1
    # via
    #   -c requirements/common-constraints.txt
    #   streamlit
python-dateutil==2.9.0.post0
    # via
    #   -c requirements/common-constraints.txt
    #   pandas
pytz==2025.2
    # via
    #   -c requirements/common-constraints.txt
    #   pandas
referencing==0.36.2
    # via
    #   -c requirements/common-constraints.txt
    #   jsonschema
    #   jsonschema-specifications
requests==2.32.3
    # via
    #   -c requirements/common-constraints.txt
    #   streamlit
rpds-py==0.25.1
    # via
    #   -c requirements/common-constraints.txt
    #   jsonschema
    #   referencing
six==1.17.0
    # via
    #   -c requirements/common-constraints.txt
    #   python-dateutil
smmap==5.0.2
    # via
    #   -c requirements/common-constraints.txt
    #   gitdb
streamlit==1.45.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements-browser.in
tenacity==9.1.2
    # via
    #   -c requirements/common-constraints.txt
    #   streamlit
toml==0.10.2
    # via
    #   -c requirements/common-constraints.txt
    #   streamlit
tornado==6.5.1
    # via
    #   -c requirements/common-constraints.txt
    #   streamlit
typing-extensions==4.14.0
    # via
    #   -c requirements/common-constraints.txt
    #   altair
    #   referencing
    #   streamlit
tzdata==2025.2
    # via
    #   -c requirements/common-constraints.txt
    #   pandas
urllib3==2.4.0
    # via
    #   -c requirements/common-constraints.txt
    #   requests
